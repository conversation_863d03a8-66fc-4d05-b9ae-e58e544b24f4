apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: envoy-gateway-custom-resources
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: https://github.com/wittignl/k8s-clusters
    path: base/infrastructure/envoy-gateway/resources
    targetRevision: HEAD
  destination:
    server: https://kubernetes.default.svc
    namespace: envoy-gateway-system
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - ServerSideApply=true
